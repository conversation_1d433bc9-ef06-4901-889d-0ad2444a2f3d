using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 底层piece或组的信息
/// </summary>
public class UnderlyingItem
{
    public bool IsGroup { get; set; }
    public JigsawPiece Piece { get; set; }
    public JigsawGroup Group { get; set; }

    public UnderlyingItem(JigsawPiece piece)
    {
        IsGroup = false;
        Piece = piece;
    }

    public UnderlyingItem(JigsawGroup group)
    {
        IsGroup = true;
        Group = group;
    }
}

/// <summary>
/// 智能层级管理器，负责管理所有拼块和thickness的层级
/// </summary>
public class JigsawLayerManager
{
    private GComponent rootContainer;
    private JigsawPanel parentPanel;
    private readonly List<JigsawPiece> pieces = new();
    // 拼块clone映射：支持多种类型的clone（thickness、shadow等）
    private readonly Dictionary<JigsawPiece, Dictionary<string, GComponent>> pieceCloneMap = new();

    // 网格索引：用于快速查找指定网格位置的拼块
    private readonly Dictionary<Vector2Int, List<JigsawPiece>> gridIndex = new();
    // 拼块位置缓存：用于快速移除拼块时定位其在网格中的位置
    private readonly Dictionary<JigsawPiece, Vector2Int> pieceGridPositionCache = new();
    public JigsawLayerManager(GComponent container, JigsawPanel panel)
    {
        rootContainer = container;
        parentPanel = panel;
    }

    /// <summary>
    /// 创建并配置clone对象的统一方法
    /// 参考thicknessClone的设置方式，为shadowClone和未来的其他clone提供统一的配置
    /// 优势：
    /// 1. 统一的创建流程，减少重复代码
    /// 2. 灵活的配置方式，通过setupAction自定义每种clone的特殊设置
    /// 3. 统一的命名规范和错误处理
    /// 4. 便于维护和扩展新的clone类型
    /// </summary>
    /// <param name="packageName">包名</param>
    /// <param name="componentName">组件名</param>
    /// <param name="childName">子对象名</param>
    /// <param name="piece">拼块</param>
    /// <param name="gameObjectName">GameObject名称（仅在Editor模式下使用）</param>
    /// <param name="setupAction">额外的设置操作，用于配置特定clone的属性</param>
    /// <returns>创建的clone对象和子loader</returns>
    private (GComponent clone, GLoader loader) CreateAndSetupClone(string packageName, string componentName, string childName, JigsawPiece piece, string gameObjectName = null, System.Action<GLoader> setupAction = null)
    {
        var clone = UIPackage.CreateObject(packageName, componentName).asCom;
        var loader = clone.GetChild(childName).asLoader;

        var url = $"ui://Z_Image_{piece.imageIndex}/piece_{piece.pieceIndex}";
        loader.url = url;

        // 执行额外的设置操作
        setupAction?.Invoke(loader);

#if UNITY_EDITOR
        if (!string.IsNullOrEmpty(gameObjectName))
        {
            clone.gameObjectName = gameObjectName + piece.pieceIndex;
        }
#endif

        return (clone, loader);
    }

    /// <summary>
    /// 确保拼块有thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void EnsurePieceHasThickness(JigsawPiece piece)
    {
        if (piece == null) return;

        // 检查是否已经有thickness
        var existingThickness = GetThicknessFor(piece);
        if (existingThickness != null) return;

        // 创建新的thickness
        try
        {
            if (rootContainer == null)
            {
                Debug.LogError("SmartLayerManager: rootContainer is null!");
                return;
            }
            var pieceContainer = parentPanel.GetPieceContainer();
            // 创建thickness clone
            var (thicknessClone, thickness) = CreateAndSetupClone("Jigsaw", "JigsawThickness", "thickness", piece, "JigsawThickness");

            Log.Info("pieceContainer:" + pieceContainer);
            Log.Info("thicknessClone:" + thicknessClone);
            Log.Info("thickness:" + thickness);

            // 创建shadow clone
            var (shadowClone, shadow) = CreateAndSetupClone("Jigsaw", "JigsawShadow", "shadow", piece, "JigsawShadow", loader =>
            {
                // 使用支持合批的ShadowFilter
                loader.filter = ShadowFilter.CreateWithPreset(ShadowFilter.ShadowPreset.Normal);
                loader.visible = true;
            });
            Log.Info("shadowClone:" + shadowClone);
            Log.Info("shadow:" + shadow);

            pieceContainer.AddChild(shadowClone);
            pieceContainer.AddChild(thicknessClone);

            // 示例：创建其他类型的clone
            // var (highlightClone, highlight) = CreateAndSetupClone("Jigsaw", "JigsawHighlight", "highlight", piece, "JigsawHighlight", loader =>
            // {
            //     loader.color = Color.yellow;
            //     loader.alpha = 0.5f;
            // });

            // var (glowClone, glow) = CreateAndSetupClone("Jigsaw", "JigsawGlow", "glow", piece, "JigsawGlow", loader =>
            // {
            //     loader.filter = GlowFilter.CreateWithPreset(GlowFilter.GlowPreset.Soft);
            //     loader.visible = false; // 默认隐藏，需要时显示
            // });

            // 将thickness添加到对应的容器（默认为普通piece容器）



            // 隐藏拼块自身的thickness
            piece.SetThicknessVisible(false);

            // 建立映射关系 - 缓存所有clone以便同步坐标
            StorePieceCloneMapping(piece, "thickness", thicknessClone);
            StorePieceCloneMapping(piece, "shadow", shadowClone);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to create thickness for piece {piece.pieceIndex}: {ex.Message}");
        }
    }

    /// <summary>
    /// 存储拼块clone映射（统一方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="cloneType">clone类型（如"thickness"、"shadow"等）</param>
    /// <param name="clone">clone组件</param>
    private void StorePieceCloneMapping(JigsawPiece piece, string cloneType, GComponent clone)
    {
        if (!pieceCloneMap.ContainsKey(piece))
        {
            pieceCloneMap[piece] = new Dictionary<string, GComponent>();
        }
        pieceCloneMap[piece][cloneType] = clone;
    }

    /// <summary>
    /// 获取拼块的指定类型clone
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="cloneType">clone类型</param>
    /// <returns>clone组件</returns>
    private GComponent GetCloneFor(JigsawPiece piece, string cloneType)
    {
        if (piece == null) return null;

        if (pieceCloneMap.TryGetValue(piece, out var cloneDict) &&
            cloneDict.TryGetValue(cloneType, out var clone))
        {
            return clone;
        }

        return null;
    }

    /// <summary>
    /// 存储拼块thickness映射（兼容性方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void StorePieceThicknessMapping(JigsawPiece piece, GComponent thickness)
    {
        StorePieceCloneMapping(piece, "thickness", thickness);
    }

    /// <summary>
    /// 移除拼块的所有clone
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceClones(JigsawPiece piece)
    {
        if (piece == null) return;

        if (pieceCloneMap.TryGetValue(piece, out var cloneDict))
        {
            // 移除所有clone
            foreach (var clone in cloneDict.Values)
            {
                clone.parent?.RemoveChild(clone, true);
            }

            // 移除映射关系
            pieceCloneMap.Remove(piece);

            // 显示拼块自身的thickness
            piece.SetThicknessVisible(true);
        }
    }

    /// <summary>
    /// 移除拼块的thickness（兼容性方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceThickness(JigsawPiece piece)
    {
        RemovePieceClones(piece);
    }

    /// <summary>
    /// 更新所有clone的位置坐标（统一方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    private void UpdateAllClonePositions(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        if (!pieceCloneMap.TryGetValue(piece, out var cloneDict)) return;

        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);

            // 更新所有clone的位置
            foreach (var clone in cloneDict.Values)
            {
                if (clone == null || clone.isDisposed) continue;

                var offsetX = (piece.width - clone.width) / 2f;
                var offsetY = (piece.height - clone.height) / 2f;

                // 转换为clone父容器的本地坐标
                if (clone.parent != null)
                {
                    Vector2 cloneLocalPos = clone.parent.GlobalToLocal(pieceGlobalPos);
                    clone.SetXY(cloneLocalPos.x + offsetX, cloneLocalPos.y + offsetY);
                }
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }

    /// <summary>
    /// 更新thickness的位置坐标（兼容性方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void UpdateThicknessPosition(JigsawPiece piece, GComponent thickness)
    {
        UpdateAllClonePositions(piece);
    }

    public void AddPiece(JigsawPiece piece)
    {
        pieces.Add(piece);
    }

    /// <summary>
    /// 从层级管理系统移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UnregisterPiece(JigsawPiece piece)
    {
        RemovePieceThickness(piece);
    }

    /// <summary>
    /// 开始拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StartDragging(JigsawPiece piece)
    {
        EnsurePieceHasThickness(piece);
        MovePieceToContainer(piece);
    }

    /// <summary>
    /// 停止拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StopDragging(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;
        MovePieceToContainer(piece);
        UpdatePieceGridIndex(piece);

        // 检查piece或组的格子下是否有其他piece或组，根据数量看是否需要置顶
        CheckAndHandleUnderlyingPieces(piece);
    }

    /// <summary>
    /// 检查并处理piece或组下面的其他piece或组
    /// </summary>
    /// <param name="piece">当前拼块</param>
    private void CheckAndHandleUnderlyingPieces(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || parentPanel == null) return;

        // 获取当前piece或组占据的所有网格位置
        var occupiedGridPositions = GetOccupiedGridPositions(piece);

        // 查找在相同网格位置的其他piece或组
        var underlyingPiecesAndGroups = FindUnderlyingPiecesAndGroups(piece, occupiedGridPositions);

        // 处理找到的底层piece或组，按数量从大到小排序后置顶
        ProcessUnderlyingItemsBySize(piece, underlyingPiecesAndGroups);
    }

    /// <summary>
    /// 按照piece数量处理底层items，从大到小置顶（最小的在最上面）
    /// </summary>
    /// <param name="currentPiece">当前拼块</param>
    /// <param name="underlyingItems">底层items</param>
    private void ProcessUnderlyingItemsBySize(JigsawPiece currentPiece, List<UnderlyingItem> underlyingItems)
    {
        if (underlyingItems.Count == 0) return;

        var currentPieceCount = GetCurrentPieceOrGroupCount(currentPiece);

        // 筛选出需要置顶的items（数量比当前piece或组少的）
        var itemsToProcess = new List<(UnderlyingItem item, int count)>();

        foreach (var item in underlyingItems)
        {
            int itemCount = item.IsGroup ? item.Group.Count : 1;
            if (itemCount < currentPieceCount)
            {
                itemsToProcess.Add((item, itemCount));
            }
        }

        if (itemsToProcess.Count == 0) return;

        // 按数量从大到小排序，这样先置顶数量大的，最后置顶数量小的（小的在最上面）
        itemsToProcess.Sort((a, b) => b.count.CompareTo(a.count));

        // 依次置顶
        foreach (var (item, count) in itemsToProcess)
        {
            if (item.IsGroup)
            {
                BringGroupToTop(item.Group);
            }
            else
            {
                BringPieceToTop(item.Piece);
            }
        }
    }

    /// <summary>
    /// 获取piece或组占据的所有网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置列表</returns>
    private List<Vector2Int> GetOccupiedGridPositions(JigsawPiece piece)
    {
        var gridPositions = new List<Vector2Int>();
        var group = piece.GetGroup();

        if (group != null)
        {
            // 如果是组，获取组中所有piece的网格位置
            foreach (var groupPiece in group.Pieces)
            {
                var gridPos = GetPieceGridPosition(groupPiece);
                if (!gridPositions.Contains(gridPos))
                {
                    gridPositions.Add(gridPos);
                }
            }
        }
        else
        {
            // 如果是单个piece，只获取该piece的网格位置
            gridPositions.Add(GetPieceGridPosition(piece));
        }

        return gridPositions;
    }

    /// <summary>
    /// 获取拼块在操作层中的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int GetPieceGridPosition(JigsawPiece piece)
    {
        Vector2 pieceCenter = piece.LocalToGlobal(new Vector2(piece.width * 0.5f, piece.height * 0.5f));
        Vector2 operationLayerPos = parentPanel.GlobalToOperationLayerLocal(pieceCenter);
        return parentPanel.GetGridPosition(operationLayerPos);
    }

    /// <summary>
    /// 查找在相同网格位置的其他piece或组（优化版本，使用网格索引）
    /// </summary>
    /// <param name="currentPiece">当前拼块</param>
    /// <param name="occupiedGridPositions">占据的网格位置</param>
    /// <returns>底层piece或组列表</returns>
    private List<UnderlyingItem> FindUnderlyingPiecesAndGroups(JigsawPiece currentPiece, List<Vector2Int> occupiedGridPositions)
    {
        var underlyingItems = new List<UnderlyingItem>();
        var processedGroups = new HashSet<JigsawGroup>();
        var currentGroup = currentPiece.GetGroup();

        // 使用网格索引快速查找
        foreach (var gridPos in occupiedGridPositions)
        {
            if (!gridIndex.ContainsKey(gridPos)) continue;

            foreach (var otherPiece in gridIndex[gridPos])
            {
                if (otherPiece == currentPiece || otherPiece.isDisposed) continue;

                // 如果是同一个组的piece，跳过
                var otherGroup = otherPiece.GetGroup();
                if (currentGroup != null && otherGroup == currentGroup) continue;

                if (otherGroup != null)
                {
                    // 如果是组的一部分且该组还未处理过
                    if (!processedGroups.Contains(otherGroup))
                    {
                        underlyingItems.Add(new UnderlyingItem(otherGroup));
                        processedGroups.Add(otherGroup);
                    }
                }
                else
                {
                    // 单个piece
                    underlyingItems.Add(new UnderlyingItem(otherPiece));
                }
            }
        }

        return underlyingItems;
    }

    /// <summary>
    /// 获取当前piece或组的数量
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>piece数量</returns>
    private int GetCurrentPieceOrGroupCount(JigsawPiece piece)
    {
        var group = piece.GetGroup();
        return group?.Count ?? 1;
    }

    /// <summary>
    /// 将组置顶
    /// </summary>
    /// <param name="group">组</param>
    private void BringGroupToTop(JigsawGroup group)
    {
        if (group == null) return;

        foreach (var piece in group.Pieces)
        {
            BringPieceToTop(piece);
        }
    }

    /// <summary>
    /// 将单个piece置顶
    /// </summary>
    /// <param name="piece">拼块</param>
    private void BringPieceToTop(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        // 调用MovePieceToContainer将piece置顶
        MovePieceToContainer(piece);
    }

    /// <summary>
    /// 将拼块移动到指定的容器
    /// </summary>
    /// <param name="piece">拼块</param>
    private void MovePieceToContainer(JigsawPiece curPiece)
    {
        if (curPiece == null) return;

        var targetContainer = parentPanel.GetPieceContainer();
        var group = curPiece.GetGroup();
        if (group != null)
        {
            var piecesToMove = group.Pieces;

            // 移动shadow
            foreach (var piece in piecesToMove)
            {
                var shadow = GetShadowFor(piece);
                if (shadow != null && !shadow.isDisposed)
                {
                    MoveToContainer(shadow, targetContainer);
                }
            }

            // 移动thickness
            foreach (var piece in piecesToMove)
            {
                var thickness = GetThicknessFor(piece);
                if (thickness != null && !thickness.isDisposed)
                {
                    MoveToContainer(thickness, targetContainer);
                }
            }

            // 移动拼块
            foreach (var piece in piecesToMove)
            {
                MoveToContainer(piece, targetContainer);
            }

            // 更新所有clone坐标
            foreach (var piece in piecesToMove)
            {
                UpdateAllClonePositions(piece);
            }
        }
        else
        {
            // 移动shadow
            var shadow = GetShadowFor(curPiece);
            if (shadow != null && !shadow.isDisposed)
            {
                MoveToContainer(shadow, targetContainer);
            }

            // 移动thickness
            var thickness = GetThicknessFor(curPiece);
            if (thickness != null && !thickness.isDisposed)
            {
                MoveToContainer(thickness, targetContainer);
            }

            // 移动拼块
            MoveToContainer(curPiece, targetContainer);

            // 更新所有clone坐标
            UpdateAllClonePositions(curPiece);
        }
    }

    /// <summary>
    /// 更新拼块位置（同步所有clone坐标）
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePiecePosition(JigsawPiece piece)
    {
        UpdateAllClonePositions(piece);
    }

    /// <summary>
    /// 更新拼块在网格索引中的位置
    /// </summary>
    /// <param name="piece">拼块</param>
    private void UpdatePieceGridIndex(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        // 先从旧位置移除
        RemovePieceFromGridIndex(piece);

        // 添加到新位置
        var gridPos = GetPieceGridPosition(piece);
        if (!gridIndex.ContainsKey(gridPos))
        {
            gridIndex[gridPos] = new List<JigsawPiece>();
        }
        gridIndex[gridPos].Add(piece);

        // 更新位置缓存
        pieceGridPositionCache[piece] = gridPos;
    }

    /// <summary>
    /// 从网格索引中移除拼块（优化版本，使用位置缓存）
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceFromGridIndex(JigsawPiece piece)
    {
        if (piece == null) return;

        // 使用缓存快速定位并移除
        if (pieceGridPositionCache.TryGetValue(piece, out Vector2Int cachedPos))
        {
            if (gridIndex.ContainsKey(cachedPos))
            {
                gridIndex[cachedPos].Remove(piece);
                if (gridIndex[cachedPos].Count == 0)
                {
                    gridIndex.Remove(cachedPos);
                }
            }
            pieceGridPositionCache.Remove(piece);
        }
        else
        {
            // 如果缓存中没有，回退到遍历方式（兼容性）
            var keysToRemove = new List<Vector2Int>();
            foreach (var kvp in gridIndex)
            {
                kvp.Value.Remove(piece);
                if (kvp.Value.Count == 0)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            // 清理空的网格位置
            foreach (var key in keysToRemove)
            {
                gridIndex.Remove(key);
            }
        }
    }

    /// <summary>
    /// 将对象移动到指定容器
    /// </summary>
    /// <param name="obj">要移动的对象</param>
    /// <param name="targetContainer">目标容器</param>
    private void MoveToContainer(GObject obj, GComponent targetContainer)
    {
        if (obj == null || targetContainer == null || obj.isDisposed) return;

        try
        {
            // 记录当前的位置信息
            Vector2 globalPos = Vector2.zero;
            if (obj.parent != null)
            {
                globalPos = obj.LocalToGlobal(Vector2.zero);
            }

            obj.parent?.RemoveChild(obj, false);

            // 添加到新容器
            targetContainer.AddChild(obj);

            // 恢复位置
            if (obj.parent != null)
            {
                Vector2 localPos = targetContainer.GlobalToLocal(globalPos);
                obj.SetXY(localPos.x, localPos.y);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to move object to container: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取拼块对应的thickness（兼容性方法）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>thickness组件</returns>
    public GComponent GetThicknessFor(JigsawPiece piece)
    {
        return GetCloneFor(piece, "thickness");
    }

    /// <summary>
    /// 获取拼块对应的shadow
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>shadow组件</returns>
    public GComponent GetShadowFor(JigsawPiece piece)
    {
        return GetCloneFor(piece, "shadow");
    }

    /// <summary>
    /// 获取拼块的所有clone
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>clone字典</returns>
    public Dictionary<string, GComponent> GetAllClonesFor(JigsawPiece piece)
    {
        if (piece == null) return null;

        if (pieceCloneMap.TryGetValue(piece, out var cloneDict))
        {
            return new Dictionary<string, GComponent>(cloneDict); // 返回副本以防外部修改
        }

        return null;
    }

    /// <summary>
    /// 获取所有已注册的拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetAllRegisteredPieces()
    {
        return pieces;
    }

    /// <summary>
    /// 清空所有层级信息
    /// </summary>
    public void Clear()
    {
        // 清理所有clone
        foreach (var piece in pieceCloneMap.Keys.ToList())
        {
            RemovePieceClones(piece);
        }

        pieces.Clear();
        pieceCloneMap.Clear();
        gridIndex.Clear();
        pieceGridPositionCache.Clear();
    }

    /// <summary>
    /// 销毁层级管理器
    /// </summary>
    public void Dispose()
    {
        Clear();
    }
}