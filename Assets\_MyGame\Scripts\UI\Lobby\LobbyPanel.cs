using System;
using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

public class LobbyPanel : Panel
{
    public LobbyPanel()
    {
        packName = "Lobby";
        compName = "LobbyPanel";
    }

    private RedPointSystem redPointSystem;
    private SequenceManager sequenceManager;

    private GList listJigsaw;
    protected override void DoInitialize()
    {
        redPointSystem = SystemFacade.RedPointSystem;
        redPointSystem.OnNeedRefresh += RefreshRedPoint;

        sequenceManager = new SequenceManager();

        FixUIOffset.FixTop(contentPane.GetChild("boxHead"));

        InitButtons();

        var jigsawContainer = contentPane.GetChild("jigsawContainer").asCom;
        listJigsaw = jigsawContainer.GetChild("listJigsaw").asList;
        listJigsaw.itemRenderer = UpdateJigsawItem;
        listJigsaw.onClickItem.Add(OnClickJigsawItem);
        listJigsaw.numItems = 5;


        btnStart.text = GameGlobal.Level.ToString();

        // new CoinBar(contentPane.GetChild("coinBar"));
        new HeartBar(contentPane.GetChild("heartBar"));

        var imgLogo = contentPane.GetChild("imgLogo").asLoader;
#if WXGAME
        imgLogo.url = "ui://Loading/logo";
#else
        imgLogo.url = "ui://Loading/logo2";
#endif

        Report.Instance.SetCurrentChapterLevel(GameGlobal.Level);
        Report.Instance.SetCurrentStamina(SystemFacade.StaminaSystem.GetCurrentStamina());
        Report.Instance.UpdateEventBaseParams();

        RefreshRedPoint();
        NotifyMgr.On(NotifyNames.OnWxShow, this, OnWxShow);


        if (Session.DyEnterFromSidebar)
        {
            Report.Instance.ReportCheckSceneClickActive(true);
        }
        if (Session.DyEnterFromDesktop)
        {
            Report.Instance.ReportAddShortcutClickActive(true);
        }
    }

    private void OnClickJigsawItem(EventContext context)
    {
        var item = context.sender as GObject;
        var pictureId = item.data;

        Create((JigsawPanel panel) =>
        {
            panel.SetData(pictureId);
        });
    }

    private void UpdateJigsawItem(int index, GObject item)
    {
        var txtCount = item.asCom.GetChild("txtCount").asTextField;
        var image = item.asCom.GetChild("image").asLoader;
        txtCount.text = (index + 1).ToString();
        image.url = $"bundle://UI/JigsawPreview/{index + 1}";
    }

    private void OnWxShow()
    {
        RefreshRedPoint();
    }


    protected override void OnHide()
    {
        redPointSystem.OnNeedRefresh -= RefreshRedPoint;

        sequenceManager.Clear();
    }

    #region 小红点
    private GButton btnStart, btnTheme, btnDailyReward, btnDailyBattle, btnInvite;
    private GButton btnSidebar, btnAddDesktop;
    private GObject btnDyFeed;
    private void InitButtons()
    {
        btnStart = contentPane.GetChild("btnStart").asButton;
        btnTheme = contentPane.GetChild("btnTheme").asButton;
        btnDailyReward = contentPane.GetChild("btnDailyReward").asButton;
        btnDailyBattle = contentPane.GetChild("btnDailyBattle").asButton;
        btnInvite = contentPane.GetChild("btnInvite").asButton;
        btnSidebar = contentPane.GetChild("btnSidebar").asButton;

        btnAddDesktop = contentPane.GetChild("btnAddDesktop").asButton;
        btnDyFeed = contentPane.GetChild("btnDyFeed");

#if WXGAME
        btnDailyReward.visible = true;
        btnSidebar.visible = false;
#elif DYGAME
        btnSidebar.visible = Session.DySupportSideBar;
        if(btnDyFeed != null)
        {
            btnDyFeed.visible = true;
        }
#endif
    }

    private void RefreshRedPoint()
    {
        bool hasRedPoint;
        //邀请好友奖励
        hasRedPoint = SystemFacade.ActivitySystem.CanClaim(ActivityIds.InviteFriends);
        EnableButtonRedPoint(btnInvite, hasRedPoint);

        //每日挑战
        hasRedPoint = SystemFacade.NewFunctionSystem.IsFunOpen(FunIds.DailyChallenge, false) && !SystemFacade.DailyChallengeSystem.IsTodayChallengeCompleted();
        EnableButtonRedPoint(btnDailyBattle, hasRedPoint);

#if WXGAME
        //每日奖励
        hasRedPoint = SystemFacade.ActivitySystem.CanClaim(ActivityIds.CollectToMiniProgram) && Session.IsEnterFromMiniProgram;
        EnableButtonRedPoint(btnDailyReward, hasRedPoint);

#elif DYGAME
        //添加桌面
        hasRedPoint = SystemFacade.ActivitySystem.CanClaim(ActivityIds.AddDesktop);
        EnableButtonRedPoint(btnAddDesktop, hasRedPoint);

        //侧边栏入口
        hasRedPoint = SystemFacade.ActivitySystem.CanClaim(ActivityIds.Sidebar);
        EnableButtonRedPoint(btnSidebar, hasRedPoint);

        //抖音直出
        hasRedPoint = SystemFacade.ActivitySystem.CanClaim(ActivityIds.DYFeed) && Session.DyFeedSubscribed;
        EnableButtonRedPoint(btnDyFeed, hasRedPoint);
#endif
    }
    #endregion

    #region 弹窗队列
    protected override void OnShow()
    {
        sequenceManager.Clear();
        // sequenceManager.Enqueue(ShowLoginReward);
        // sequenceManager.Enqueue(ShowGetStar);
        sequenceManager.Enqueue(ShowRewardPanels);
        // sequenceManager.Enqueue(ShowCacheProgress);
        sequenceManager.ProcessQueue();
    }

    private void ShowLoginReward(SequenceManager sequence)
    {
        if (SystemFacade.ActivitySystem.CanClaim(ActivityIds.DailyLogin))
        {
            SystemFacade.ActivitySystem.TryClaimReward(ActivityIds.DailyLogin);

            var skinSystem = SystemFacade.SkinSystem;
            var themeId = skinSystem.GetCollectingThemeId();
            var iconId = skinSystem.GetRandomUncollectedThemeIconId(themeId);
            skinSystem.CollectResource(themeId, iconId);

            TipMgr.ShowThemeIcons(themeId, new int[] { iconId }, btnTheme, () =>
            {
                if (contentPane == null || contentPane.isDisposed)
                    return;
                UITweenUtil.ScaleUpAndDown(btnTheme);

                sequence.ProcessQueue();
            });
        }
        else
        {
            sequence.ProcessQueue();
        }
    }

    /// <summary>
    /// 检测是否有未完成的游戏进度
    /// </summary>
    private void ShowCacheProgress(SequenceManager sequence)
    {
        var gameProgress = SystemManager.Inst.GetGlobalSystem<GameProgressSystem>();
        if (gameProgress.RestoreProgress())
        {
            TipMgr.ShowMsgBox(LangUtil.GetText("txtCheckLocalGame"),//检测到有未完成的游戏进度，是否继续？
                okAction: () =>
                {
                    var battleType = gameProgress.GetProgressData().battleType;
                    if (battleType == BattleType.Normal)
                    {
                        EnterBattle(true);
                    }
                    else if (battleType == BattleType.DailyChallenge)
                    {
                        EnterDailyBattle();
                    }
                    sequence.ProcessQueue();
                },
                cancelAction: () =>
                {
                    gameProgress.ClearProgress();
                    sequence.ProcessQueue();
                },
                okLabel: LangUtil.GetText("txtContinueGame")
            );
        }
        else
        {
            sequence.ProcessQueue();
        }
    }

    /// <summary>
    /// 按照当前索引顺序显示弹窗，并在显示完后更新索引
    /// </summary>
    private void ShowRewardPanels(SequenceManager sequence)
    {
        var activitySystem = SystemFacade.ActivitySystem;

        var activityList = new List<int>();
#if DYGAME
        if(SystemFacade.ActivitySystem.CanClaim(ActivityIds.DYFeed) && !Session.DyEnterFromFeed)
        {
            activityList.Add(ActivityIds.DYFeed);
        }

        activityList.Add(ActivityIds.Sidebar);
        activityList.Add(ActivityIds.AddDesktop);
#elif WXGAME
        activityList.Add(ActivityIds.CollectToMiniProgram);
#endif

        activityList.Add(ActivityIds.InviteFriends);

        int[] panelTypes = activityList.ToArray();
        int totalPanelCount = panelTypes.Length;

        //test
        // StorageMgr.CurrentRewardPanelIndex=0;

        // 从当前索引开始循环检查
        for (int i = 0; i < totalPanelCount; i++)
        {
            // 计算当前要检查的弹窗索引，循环到最后再从头开始
            int currentIndex = (Session.CurrentRewardPanelIndex + i) % totalPanelCount;
            int panelType = panelTypes[currentIndex];

            // 检查当前类型的弹窗是否可以显示
            if (activitySystem.CanClaim(panelType))
            {
                // 更新索引为下一个要显示的弹窗
                Session.CurrentRewardPanelIndex = (currentIndex + 1) % totalPanelCount;

                // 根据类型显示对应的弹窗
                switch (panelType)
                {
                    case ActivityIds.DYFeed:
                        ShowDYFeedPanel(() =>
                        {
                            sequence.ProcessQueue();
                        });
                        return;

                    case ActivityIds.Sidebar:
                        ShowDYSidebarPanel(() =>
                        {
                            sequence.ProcessQueue();
                        });
                        return;

                    case ActivityIds.AddDesktop:
                        ShowDesktopDYPanel(() =>
                        {
                            sequence.ProcessQueue();
                        });
                        return;

                    case ActivityIds.InviteFriends:
                        ShowInviteFriendsPanel(() =>
                        {
                            sequence.ProcessQueue();
                        });
                        return;

                    case ActivityIds.CollectToMiniProgram:
                        ShowDailyRewardPanel(() =>
                        {
                            sequence.ProcessQueue();
                        });
                        return;
                }
            }
        }

        // 如果没有需要显示的弹窗，继续处理队列
        sequence.ProcessQueue();
    }


    #endregion


    private void EnableButtonRedPoint(GObject btn, bool enable)
    {
        if (btn == null)
            return;

        var redPoint = btn.asButton.GetChild("imgRedDot");
        if (redPoint == null)
            return;

        redPoint.visible = enable;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            // case "btnArrowLeft":
            //     OnArrowLeftClick();
            //     break;
            // case "btnArrowRight":
            //     OnArrowRightClick();
            //     break;
            case "btnStart":
                EnterBattle();
                break;
            case "btnDailyBattle":
                if (SystemFacade.NewFunctionSystem.IsFunOpen(FunIds.DailyChallenge, true))
                {
                    if (SystemFacade.DailyChallengeSystem.IsTodayChallengeCompleted())
                    {
                        TipMgr.ShowTip(LangUtil.GetText("txtAlreadyCompleteDailyBattle"));//今日已完成挑战
                    }
                    else
                    {
                        Create((DailyChallengePanel panel) =>
                        {
                            panel.OnClosed = (type) =>
                            {
                                if (type == DailyChallengePanel.CloseType_Enter)
                                {
                                    EnterDailyBattle();
                                }
                            };
                        });
                    }
                }
                break;
            case "btnSetting":
                Create((SettingPanel panel) => panel.SetData(SettingPanel.SettingViewMode.Setting), cache: true);
                break;
            case "heartBar":
                if (SystemManager.Inst.GetGlobalSystem<StaminaSystem>().GetTimeUntilNextRecovery() > 0)
                {
                    Create<BuyHeartPanel>();
                }
                else
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtFullStaminaTip"));//体力已满
                }

                break;
            case "btnTheme":
                Create<ChangeThemePanel>();
                break;
            case "btnAddDesktop":
#if WXGAME
                Create<AddDesktopPanel>();
#elif DYGAME
                ShowDesktopDYPanel();
#endif
                break;
            case "btnDailyReward":
                ShowDailyRewardPanel();
                break;
            case "btnInvite":
                ShowInviteFriendsPanel();
                break;
            case "btnDyFeed":
                ShowDYFeedPanel();
                break;
            case "btnRank":
                Create<RankPanel>();
                break;
            case "btnPrgCollect":
                HandlerManager.Inst.SwitchHandler<JigsawHandler>();
                break;
            case "starBar":
                Create<GetStarPanel>();
                break;
            case "btnSidebar":
                ShowDYSidebarPanel();
                break;
        }
    }

    private void ShowDailyRewardPanel(Action onClose = null)
    {
        Create((DailyRewardPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == DailyRewardPanel.CloseType_GetReward)
                {
                    RefreshRedPoint();

                    var skinSystem = SystemFacade.SkinSystem;
                    var themeId = skinSystem.GetCollectingThemeId();
                    var iconId = skinSystem.GetRandomUncollectedThemeIconId(themeId);
                    skinSystem.CollectResource(themeId, iconId);

                    TipMgr.ShowThemeIcons(themeId, new int[] { iconId }, btnTheme, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;
                        UITweenUtil.ScaleUpAndDown(btnTheme);

                        onClose?.Invoke();
                    });
                }
                else
                {
                    onClose?.Invoke();
                }
            };
        });
    }

    private void ShowDYFeedPanel(Action onClose = null)
    {
        Create((DYFeedPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == DYFeedPanel.CloseType_GetReward)
                {
                    RefreshRedPoint();

                    // 获取奖励并添加到背包
                    ItemVo[] rewards = SystemFacade.ActivitySystem.GetActivityRewards(ActivityIds.DYFeed);
                    foreach (var reward in rewards)
                    {
                        SystemFacade.ItemSystem.AddItem(reward.itemId, reward.count);
                    }

                    // 显示奖励提示
                    TipMgr.ShowRewards(rewards, btnStart, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;
                        UITweenUtil.ScaleUpAndDown(btnStart);

                        onClose?.Invoke();
                    });
                }
                else
                {
                    onClose?.Invoke();
                }
            };
        });
    }

    private void ShowDYSidebarPanel(Action onClose = null)
    {
        if (!Session.DySupportSideBar)
        {
            onClose?.Invoke();
            return;
        }
        Create((DYSidebarPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == DYSidebarPanel.CloseType_GetReward)
                {
                    RefreshRedPoint();

                    ItemVo[] rewards = SystemFacade.ActivitySystem.GetActivityRewards(ActivityIds.Sidebar);

                    // 添加道具到玩家背包
                    foreach (var reward in rewards)
                    {
                        SystemFacade.ItemSystem.AddItem(reward.itemId, reward.count);
                    }

                    // 显示奖励提示
                    TipMgr.ShowRewards(rewards, btnStart, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;
                        UITweenUtil.ScaleUpAndDown(btnStart);

                        onClose?.Invoke();
                    });
                }
                else
                {
                    onClose?.Invoke();
                }
            };
        });
    }

    private void ShowInviteFriendsPanel(Action onClose = null)
    {
        Create((InviteFriendsPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == InviteFriendsPanel.CloseType_GetReward)
                {
                    RefreshRedPoint();

                    // 获取奖励并添加到背包
                    ItemVo[] rewards = SystemFacade.ActivitySystem.GetActivityRewards(ActivityIds.InviteFriends);
                    foreach (var reward in rewards)
                    {
                        SystemFacade.ItemSystem.AddItem(reward.itemId, reward.count);
                    }

                    // 显示奖励提示
                    TipMgr.ShowRewards(rewards, btnStart, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;
                        UITweenUtil.ScaleUpAndDown(btnStart);

                        onClose?.Invoke();
                    });
                }
                else
                {
                    onClose?.Invoke();
                }
            };
        });
    }

    private void ShowDesktopDYPanel(Action onClose = null)
    {
        Create((AddDesktopDYPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == AddDesktopDYPanel.CloseType_GetReward)
                {
                    RefreshRedPoint();

                    ItemVo[] rewards = SystemFacade.ActivitySystem.GetActivityRewards(ActivityIds.AddDesktop);

                    // 添加道具到玩家背包
                    foreach (var reward in rewards)
                    {
                        SystemFacade.ItemSystem.AddItem(reward.itemId, reward.count);
                    }

                    // 显示奖励提示
                    TipMgr.ShowRewards(rewards, btnStart, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;
                        UITweenUtil.ScaleUpAndDown(btnStart);

                        onClose?.Invoke();
                    });
                }
                else
                {
                    onClose?.Invoke();
                }
            };
        });
    }

    private void EnterBattle(bool ignoreStaminaCost = false)
    {
        if (SystemFacade.StaminaSystem.GetCurrentStamina() > 0)
        {
            GameGlobal.BattleType = BattleType.Normal;
            var isEnterSuccess = new CmdEnterBattle().Execute(ignoreStaminaCost: ignoreStaminaCost);
            if (isEnterSuccess)
            {
                Hide();
            }
        }
        else
        {
            TipMgr.ShowTip(LangUtil.GetText("txtNotEnoughStamina"));//体力不足
            Create<BuyHeartPanel>();
        }
    }

    private void EnterDailyBattle()
    {
        if (SystemFacade.DailyChallengeSystem.IsTodayChallengeCompleted())
        {
            TipMgr.ShowTip(LangUtil.GetText("txtAlreadyCompleteDailyBattle"));//今日已完成挑战
        }
        else
        {
            GameGlobal.BattleType = BattleType.DailyChallenge;
            var isEnterSuccess = new CmdEnterBattle().Execute(SystemFacade.DailyChallengeSystem.GetCurrentChallengeGate(), true);
            if (isEnterSuccess)
            {
                LoadingPanel.Show();
                Hide();
            }
        }
    }
}
