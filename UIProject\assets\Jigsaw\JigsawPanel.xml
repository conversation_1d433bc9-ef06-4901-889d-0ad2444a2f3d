<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280">
  <displayList>
    <component id="n0_feik" name="oprationLayer" src="feik1" fileName="CompOperation.xml" xy="32,138" touchable="false">
      <relation target="" sidePair="center-center,middle-middle"/>
    </component>
    <graph id="n6_feik" name="n6" xy="29,1041" size="665,199" type="rect" lineSize="0" fillColor="#ff666666">
      <relation target="" sidePair="center-center,middle-middle"/>
    </graph>
    <list id="n5_feik" name="listStorage" xy="33,1041" size="660,198" layout="row" overflow="scroll" scroll="horizontal" colGap="-50" defaultItem="ui://ypd0wm93feik3" autoItemSize="false" vAlign="middle" autoClearItems="true" scrollItemToViewOnClick="false">
      <relation target="" sidePair="center-center,middle-middle"/>
      <item/>
      <item/>
      <item/>
      <item/>
      <item/>
    </list>
    <component id="n7_nns2" name="btnPause" src="nns2b" fileName="btnPause.xml" xy="18,16"/>
  </displayList>
</component>