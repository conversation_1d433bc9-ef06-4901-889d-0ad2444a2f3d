﻿internal class JigsawHandler : HandlerBase
{
    private JigsawPanel jigsawPanel;
    public override void OnEnter()
    {
        LoadingPanel.Show();
        LoadingPanel.SetProgress(1);

        SoundManager.PlayBg("bgm");

        Panel.Create((JigsawPanel panel) =>
        {
            jigsawPanel = panel;
            LoadingPanel.HideWhenFullProgress();
        }, isAsync: true);
    }

    public override void OnExit()
    {
        jigsawPanel?.Hide();
    }
}