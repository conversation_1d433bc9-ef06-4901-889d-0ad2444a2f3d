using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using FairyGUI;
using UnityEngine;

public class JigsawPanel : Panel
{
    public JigsawPanel()
    {
        packName = "Jigsaw";
        compName = "JigsawPanel";
    }

    public int col = 6;
    public int row = 8;
    public int pieceSize = 110;
    public int pieceCompSize = 180;


    private OperationLayer oprationLayer;
    private GList listStorage;
    private GComponent oprationComponent;
    private int imageIndex;
    private GComponent pieceContainer;  // 专门用于管理piece和thickness渲染的容器

    // 组管理
    private JigsawGroupManager groupManager;
    protected override void DoInitialize()
    {
        UIObjectFactory.SetPackageItemExtension($"ui://{packName}/JigsawPiece", () => { return new JigsawPiece(); });

        // 初始化组管理器
        groupManager = new JigsawGroupManager(this);

        oprationComponent = contentPane.GetChild("oprationLayer").asCom;
        listStorage = contentPane.GetChild("listStorage").asList;

        pieceContainer = new GComponent();
        pieceContainer.SetSize(contentPane.width, contentPane.height);
        pieceContainer.gameObjectName = "PieceContainer";
        pieceContainer.fairyBatching = false;
        contentPane.AddChild(pieceContainer);

        // 创建OperationLayer
        oprationLayer = new OperationLayer(oprationComponent, this, pieceContainer, col, row, pieceSize);

        // 新系统中通过层级索引管理显示顺序
        contentPane.fairyBatching = false;

        listStorage.itemRenderer = UpdateStoragePiece;
        listStorage.fairyBatching = true;

        // SetData(1);
    }

    public void SetData(int pictureId)
    {
        imageIndex = pictureId;
        FUILoader.LoadPackage($"Z_Image_{imageIndex}", () =>
        {
            if (contentPane == null || contentPane.isDisposed) return;
            listStorage.numItems = col * row;

            // TestCompletion();
        });
    }

    private void TestCompletion()
    {
        var count = listStorage.numChildren;
        for (int i = 0; i < count; i++)
        {
            var piece = listStorage.GetChildAt(0) as JigsawPiece;
            var pieceClone = piece.Clone();

            var pieceContainer = GetPieceContainer();
            pieceContainer.AddChild(pieceClone);
            var pos = GetLocalPosition(new Vector2Int(i % col, Mathf.FloorToInt(i / col)));
            pos.x += oprationComponent.x - pieceCompSize * 0.5f;
            pos.y += oprationComponent.y - pieceCompSize * 0.5f;
            pieceClone.xy = pos;

            pieceClone.isDragging = true;
            oprationLayer.OnPieceDragStart(pieceClone);
            oprationLayer.OnPiecePositionChanged(pieceClone);

            pieceClone.HandleDropToOperationLayer();
            if (i == count - 2)
                break;
        }
    }

    private void UpdateStoragePiece(int index, GObject item)
    {
        var piece = item as JigsawPiece;
        piece.scale = Vector2.one * 0.8f;
        piece.SetPiece(imageIndex, index);

        // 设置父面板引用，用于拖拽处理
        piece.SetParentPanel(this);
        // 设置存储标识
        piece.SetInStorage(true);
    }

    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        oprationLayer?.SetGridVisible(visible);
    }


    /// <summary>
    /// 获取操作层的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        return oprationLayer?.GetGridPosition(localPosition) ?? Vector2Int.zero;
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        return oprationLayer?.GetLocalPosition(gridPosition) ?? Vector2.zero;
    }

    /// <summary>
    /// 将全局坐标转换为操作层的本地坐标
    /// </summary>
    /// <param name="globalPosition">全局坐标</param>
    /// <returns>操作层本地坐标</returns>
    public Vector2 GlobalToOperationLayerLocal(Vector2 globalPosition)
    {
        return oprationComponent.GlobalToLocal(globalPosition);
    }

    /// <summary>
    /// 将操作层本地坐标转换为全局坐标
    /// </summary>
    /// <param name="localPosition">操作层本地坐标</param>
    /// <returns>全局坐标</returns>
    public Vector2 OperationLayerLocalToGlobal(Vector2 localPosition)
    {
        return oprationComponent.LocalToGlobal(localPosition);
    }

    /// <summary>
    /// 检查位置是否在操作层范围内
    /// </summary>
    /// <param name="operationLayerLocalPos">操作层本地坐标</param>
    /// <returns>是否在范围内</returns>
    public bool IsPositionInOperationLayer(Vector2 operationLayerLocalPos)
    {
        return operationLayerLocalPos.x >= 0 && operationLayerLocalPos.y >= 0 &&
               operationLayerLocalPos.x <= oprationComponent.width &&
               operationLayerLocalPos.y <= oprationComponent.height;
    }

    /// <summary>
    /// 获取操作层对象
    /// </summary>
    /// <returns>操作层对象</returns>
    public OperationLayer GetOperationLayer()
    {
        return oprationLayer;
    }

    /// <summary>
    /// 获取渲染容器
    /// </summary>
    /// <returns>渲染容器</returns>
    public GComponent GetPieceContainer()
    {
        return pieceContainer;
    }

    /// <summary>
    /// 检查并创建拼块组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    public void CheckAndCreateGroups(JigsawPiece newPiece)
    {
        groupManager?.CheckAndCreateGroups(newPiece);
    }

    /// <summary>
    /// 获取操作层中的所有拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetPiecesInOperationLayer()
    {
        var operationLayer = GetOperationLayer();
        var layerManager = operationLayer.GetLayerManager();

        var pieces = layerManager.GetAllRegisteredPieces();
        return pieces;
    }

    /// <summary>
    /// 获取拼图总块数
    /// </summary>
    /// <returns>总块数</returns>
    public int GetTotalPieceCount()
    {
        return col * row;
    }

    /// <summary>
    /// 触发拼图完成动画
    /// </summary>
    public void TriggerCompletionAnimation()
    {
        var completedGroup = groupManager.Groups.FirstOrDefault(g => g.Count == GetTotalPieceCount() && g.AreAllPiecesInCorrectPosition());
        if (completedGroup != null)
        {
            PlayCompletionAnimation(completedGroup);
        }
    }

    /// <summary>
    /// 播放拼图完成动画
    /// </summary>
    /// <param name="completedGroup">完成的拼块组</param>
    /// <returns>IEnumerator</returns>
    private async void PlayCompletionAnimation(JigsawGroup completedGroup)
    {
        var diagonalGroups = completedGroup.Pieces
            .GroupBy(p => p.GetOriginalGridPosition().x + p.GetOriginalGridPosition().y)
            .OrderBy(g => g.Key);

        foreach (var group in diagonalGroups)
        {
            if (contentPane == null || contentPane.isDisposed) return;
            foreach (var piece in group)
            {
                piece.TweenCompletionEffect(Color.yellow);
            }
            await UniTask.Delay(50);
        }
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnPause":
                HandlerManager.Inst.SwitchHandler<LobbyHandler>();
                break;
        }
    }
}
